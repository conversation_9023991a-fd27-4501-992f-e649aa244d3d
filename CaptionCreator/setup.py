"""
Setup script for creating Windows executable of Caption Creator using cx_Freeze
"""

from cx_Freeze import setup, Executable
import sys

# Dependencies are automatically detected, but it might need fine tuning.
build_options = {
    'packages': ['tkinter', 'PIL', 'json', 'os', 'threading', 'time'],
    'excludes': ['test_app'],
    'include_files': [],
    'optimize': 2
}

base = 'Win32GUI' if sys.platform == 'win32' else None

executables = [
    Executable(
        'caption_creator.py',
        base=base,
        target_name='CaptionCreator.exe',
        icon=None  # You can add an icon file here if you have one
    )
]

setup(
    name='Caption Creator',
    version='1.0',
    description='A tool for adding captions to photos',
    author='Shig Matsushita',
    options={'build_exe': build_options},
    executables=executables
)
