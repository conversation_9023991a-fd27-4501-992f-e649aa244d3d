# Caption Creator

A Python application for adding captions to photos with customizable fonts, colors, and user names.

## Features

- **Starter Window**: Shows program name and credits for 10 seconds
- **Setup Window**: Configure your name, font, and background color with save/load functionality
- **Caption Window**: Add captions to photos with real-time preview
- **Automatic Settings**: Saves your preferences for future use

## Requirements

- Python 3.7 or higher
- Pillow (PIL) library for image processing
- tkinter (usually included with Python)

## Installation

1. Install Python dependencies:
```bash
pip install -r requirements.txt
```

## Running the Application

```bash
python caption_creator.py
```

## Creating Windows Executable

To create a standalone Windows executable:

1. Install cx_Freeze:
```bash
pip install cx_Freeze
```

2. Build the executable:
```bash
python setup.py build
```

The executable will be created in the `build/` directory.

### Alternative: Using PyInstaller

You can also use PyInstaller to create the executable:

1. Install PyInstaller:
```bash
pip install pyinstaller
```

2. Create executable:
```bash
pyinstaller --onefile --windowed caption_creator.py
```

## How to Use

1. **Starter Window**: The application shows a welcome screen for 10 seconds
2. **Setup Window**: 
   - Enter your name
   - Select a font (or use the default MS Mincho)
   - Choose a background color for the caption strip
   - Click "Next" when all fields are configured
3. **Caption Window**:
   - Click "Select a photo" to choose an image file
   - Type your caption in the text box
   - See real-time preview of the caption on your photo
   - Click "Finalize the caption" to save the captioned image
   - Use "Start over again" to work on another photo

## Technical Details

- Caption strip height is 5% of the photo height
- Caption text starts 2 character widths from the left edge
- User name appears 2 character widths from the right edge
- Font size automatically adjusts to fit the caption strip
- Settings are saved in `caption_creator_settings.json`
- Supports common image formats: JPG, PNG, BMP, GIF, TIFF

## Assumptions Made

Based on the specification, the following assumptions were made:

- **Colors**: "Whitish pale purple" background is implemented as #E6E0F0
- **Text Color**: Caption text is black for good contrast
- **Image Formats**: Supports common formats (JPG, PNG, BMP, GIF, TIFF)
- **Font Selection**: Uses system font dialog for font selection
- **Error Handling**: Basic error handling for file operations and image processing
- **Window Sizing**: Reasonable default window sizes that can be adjusted

## Troubleshooting

- If fonts don't display correctly, the application will fall back to Arial or default system font
- Make sure you have write permissions to the directory containing your photos
- Large images are automatically resized for display but saved at original resolution

## Credits

Coded by Copilot as directed by Shig Matsushita, August, 2025.
