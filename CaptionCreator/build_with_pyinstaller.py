#!/usr/bin/env python3
"""
Alternative build script using PyInstaller for creating Windows executable
"""

import subprocess
import sys
import os

def build_with_pyinstaller():
    """Build executable using PyInstaller"""
    
    # PyInstaller command
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--onefile',           # Create a single executable file
        '--windowed',          # Don't show console window
        '--name=CaptionCreator',  # Name of the executable
        '--add-data=caption_creator_settings.json;.' if os.path.exists('caption_creator_settings.json') else '',
        'caption_creator.py'
    ]
    
    # Remove empty add-data if settings file doesn't exist
    cmd = [arg for arg in cmd if arg]
    
    print("Building executable with PyInstaller...")
    print(f"Command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("Build successful!")
        print("Executable created in 'dist' folder")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Build failed: {e}")
        print(f"Error output: {e.stderr}")
        return False
    except FileNotFoundError:
        print("PyInstaller not found. Install it with: pip install pyinstaller")
        return False

def install_pyinstaller():
    """Install PyInstaller if not available"""
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller'], check=True)
        print("PyInstaller installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Failed to install PyInstaller: {e}")
        return False

def main():
    """Main build function"""
    print("Caption Creator - PyInstaller Build Script")
    print("=" * 50)
    
    # Check if PyInstaller is available
    try:
        subprocess.run([sys.executable, '-m', 'PyInstaller', '--version'], 
                      check=True, capture_output=True)
        print("PyInstaller is available")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("PyInstaller not found. Installing...")
        if not install_pyinstaller():
            sys.exit(1)
    
    # Build the executable
    if build_with_pyinstaller():
        print("\n" + "=" * 50)
        print("Build completed successfully!")
        print("You can find the executable in the 'dist' folder")
    else:
        print("\n" + "=" * 50)
        print("Build failed. Please check the error messages above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
