@echo off
echo Building Caption Creator Windows Executable...
echo.

REM Check if Python is installed (try python3 first, then python)
python3 --version >nul 2>&1
if errorlevel 1 (
    python --version >nul 2>&1
    if errorlevel 1 (
        echo Error: Python is not installed or not in PATH
        pause
        exit /b 1
    ) else (
        set PYTHON_CMD=python
        set PIP_CMD=pip
    )
) else (
    set PYTHON_CMD=python3
    set PIP_CMD=pip3
)

echo Using %PYTHON_CMD%

REM Install required packages
echo Installing required packages...
%PIP_CMD% install -r requirements.txt
%PIP_CMD% install cx_Freeze

REM Build the executable
echo Building executable with cx_Freeze...
%PYTHON_CMD% setup.py build

echo.
echo Build complete! Check the 'build' folder for your executable.
echo.
echo Alternative: You can also use PyInstaller by running:
echo %PYTHON_CMD% build_with_pyinstaller.py
echo.
pause
