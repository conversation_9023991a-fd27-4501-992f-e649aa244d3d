#!/usr/bin/env python3
"""
Test script for Caption Creator application
"""

import sys
import os

def test_imports():
    """Test that all required modules can be imported"""
    try:
        import tkinter
        print("✓ tkinter imported successfully")
    except ImportError as e:
        print(f"✗ tkinter import failed: {e}")
        return False
    
    try:
        import PIL
        from PIL import Image, ImageTk, ImageDraw, ImageFont
        print("✓ PIL/Pillow imported successfully")
    except ImportError as e:
        print(f"✗ PIL/Pillow import failed: {e}")
        return False
    
    try:
        import json
        print("✓ json imported successfully")
    except ImportError as e:
        print(f"✗ json import failed: {e}")
        return False
    
    return True

def test_application_class():
    """Test that the CaptionCreator class can be instantiated"""
    try:
        from caption_creator import CaptionCreator
        app = CaptionCreator()
        print("✓ CaptionCreator class instantiated successfully")
        
        # Test that required attributes exist
        required_attrs = ['name', 'font_family', 'background_color', 'bg_color', 'text_bg_color']
        for attr in required_attrs:
            if hasattr(app, attr):
                print(f"✓ Attribute '{attr}' exists")
            else:
                print(f"✗ Attribute '{attr}' missing")
                return False
        
        return True
    except Exception as e:
        print(f"✗ CaptionCreator class instantiation failed: {e}")
        return False

def main():
    """Run all tests"""
    print("Caption Creator - Test Suite")
    print("=" * 40)
    
    all_passed = True
    
    print("\n1. Testing imports...")
    if not test_imports():
        all_passed = False
    
    print("\n2. Testing application class...")
    if not test_application_class():
        all_passed = False
    
    print("\n" + "=" * 40)
    if all_passed:
        print("✓ All tests passed! The application is ready to run.")
        print("\nTo start the application, run:")
        print("python3 caption_creator.py")
    else:
        print("✗ Some tests failed. Please check the errors above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
