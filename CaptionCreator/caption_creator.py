#!/usr/bin/env python3
"""
Caption Creator - A Python application for adding captions to photos
Coded by <PERSON><PERSON><PERSON> as directed by <PERSON><PERSON>, August, 2025
"""

import tkinter as tk
from tkinter import ttk, filedialog, colorchooser, font as tkfont, messagebox
from PIL import Image, ImageTk, ImageDraw, ImageFont
import os
import json
import threading
import time

class CaptionCreator:
    def __init__(self):
        self.root = None
        self.settings_file = "caption_creator_settings.json"
        
        # Default values
        self.name = ""
        self.font_family = "MS Mincho"
        self.background_color = "white"
        self.photo_path = None
        self.original_photo = None
        self.current_photo = None
        
        # UI colors
        self.bg_color = "#E6E0F0"  # Whitish pale purple
        self.text_bg_color = "white"
        
        self.load_settings()
        
    def load_settings(self):
        """Load saved settings from file"""
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    self.name = settings.get('name', '')
                    self.font_family = settings.get('font_family', 'MS Mincho')
                    self.background_color = settings.get('background_color', 'white')
        except Exception as e:
            print(f"Error loading settings: {e}")
    
    def save_settings(self):
        """Save current settings to file"""
        try:
            settings = {
                'name': self.name,
                'font_family': self.font_family,
                'background_color': self.background_color
            }
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"Error saving settings: {e}")
    
    def show_starter_window(self):
        """Show the starter window for 10 seconds"""
        self.root = tk.Tk()
        self.root.title("Caption Creator")
        self.root.configure(bg=self.bg_color)
        self.root.geometry("600x400")
        self.root.resizable(False, False)
        
        # Center the window
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.root.winfo_screenheight() // 2) - (400 // 2)
        self.root.geometry(f"600x400+{x}+{y}")
        
        # Main title
        title_label = tk.Label(
            self.root,
            text="Caption Creator",
            font=("Arial", 36, "bold"),
            bg=self.bg_color,
            fg="darkblue"
        )
        title_label.pack(expand=True)
        
        # Credits in bottom right
        credits_frame = tk.Frame(self.root, bg=self.bg_color)
        credits_frame.pack(side=tk.BOTTOM, anchor=tk.SE, padx=20, pady=20)
        
        credits_label = tk.Label(
            credits_frame,
            text="Coded by Copilot as directed by Shig Matsushita, August, 2025.",
            font=("Arial", 10),
            bg=self.bg_color,
            fg="darkgray"
        )
        credits_label.pack()
        
        # Auto-close after 10 seconds
        self.root.after(10000, self.show_setup_window)
        
        self.root.mainloop()
    
    def show_setup_window(self):
        """Show the setup window for configuring name, font, and color"""
        # Clear the current window
        for widget in self.root.winfo_children():
            widget.destroy()
        
        self.root.title("Caption Creator - Setup")
        self.root.geometry("500x400")
        
        # Main frame
        main_frame = tk.Frame(self.root, bg=self.bg_color, padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Name section
        name_frame = tk.LabelFrame(main_frame, text="Name", bg=self.bg_color, font=("Arial", 12, "bold"))
        name_frame.pack(fill=tk.X, pady=10)
        
        self.name_var = tk.StringVar(value=self.name)
        name_entry = tk.Entry(name_frame, textvariable=self.name_var, bg=self.text_bg_color, font=("Arial", 11))
        name_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5, pady=5)
        
        save_name_btn = tk.Button(name_frame, text="Save the name", command=self.save_name)
        save_name_btn.pack(side=tk.LEFT, padx=2, pady=5)
        
        use_name_btn = tk.Button(name_frame, text="Use the saved name", command=self.use_saved_name)
        use_name_btn.pack(side=tk.LEFT, padx=2, pady=5)
        
        # Font section
        font_frame = tk.LabelFrame(main_frame, text="Font", bg=self.bg_color, font=("Arial", 12, "bold"))
        font_frame.pack(fill=tk.X, pady=10)
        
        self.font_var = tk.StringVar(value=self.font_family)
        font_entry = tk.Entry(font_frame, textvariable=self.font_var, bg=self.text_bg_color, font=("Arial", 11))
        font_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5, pady=5)
        
        save_font_btn = tk.Button(font_frame, text="Save the font", command=self.save_font)
        save_font_btn.pack(side=tk.LEFT, padx=2, pady=5)
        
        use_font_btn = tk.Button(font_frame, text="Use the saved font", command=self.use_saved_font)
        use_font_btn.pack(side=tk.LEFT, padx=2, pady=5)
        
        select_font_btn = tk.Button(font_frame, text="Select Font", command=self.select_font)
        select_font_btn.pack(side=tk.LEFT, padx=2, pady=5)
        
        # Color section
        color_frame = tk.LabelFrame(main_frame, text="Background Color", bg=self.bg_color, font=("Arial", 12, "bold"))
        color_frame.pack(fill=tk.X, pady=10)
        
        self.color_display = tk.Label(color_frame, text="Background color", bg=self.background_color, 
                                     width=20, height=2, relief=tk.RAISED)
        self.color_display.pack(side=tk.LEFT, padx=5, pady=5)
        
        save_color_btn = tk.Button(color_frame, text="Save the color", command=self.save_color)
        save_color_btn.pack(side=tk.LEFT, padx=2, pady=5)
        
        use_color_btn = tk.Button(color_frame, text="Use the saved color", command=self.use_saved_color)
        use_color_btn.pack(side=tk.LEFT, padx=2, pady=5)
        
        select_color_btn = tk.Button(color_frame, text="Select Color", command=self.select_color)
        select_color_btn.pack(side=tk.LEFT, padx=2, pady=5)
        
        # Next button (initially disabled)
        self.next_btn = tk.Button(main_frame, text="Next", command=self.show_caption_window, 
                                 state=tk.DISABLED, font=("Arial", 12, "bold"))
        self.next_btn.pack(pady=20)
        
        # Check if all settings are ready
        self.check_setup_complete()
    
    def save_name(self):
        self.name = self.name_var.get()
        self.save_settings()
        self.check_setup_complete()
    
    def use_saved_name(self):
        self.name_var.set(self.name)
        self.check_setup_complete()
    
    def save_font(self):
        self.font_family = self.font_var.get()
        self.save_settings()
        self.check_setup_complete()
    
    def use_saved_font(self):
        self.font_var.set(self.font_family)
        self.check_setup_complete()
    
    def select_font(self):
        """Open font selection dialog"""
        try:
            # Get available fonts
            available_fonts = list(tkfont.families())
            
            # Create a simple font selection window
            font_window = tk.Toplevel(self.root)
            font_window.title("Select Font")
            font_window.configure(bg=self.bg_color)
            font_window.geometry("300x400")
            
            listbox = tk.Listbox(font_window, bg=self.text_bg_color)
            listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            for font_name in sorted(available_fonts):
                listbox.insert(tk.END, font_name)
            
            def on_select():
                selection = listbox.curselection()
                if selection:
                    selected_font = listbox.get(selection[0])
                    self.font_var.set(selected_font)
                    font_window.destroy()
            
            select_btn = tk.Button(font_window, text="Select", command=on_select)
            select_btn.pack(pady=5)
            
        except Exception as e:
            messagebox.showerror("Error", f"Font selection error: {e}")
    
    def save_color(self):
        self.background_color = self.color_display.cget('bg')
        self.save_settings()
        self.check_setup_complete()
    
    def use_saved_color(self):
        self.color_display.configure(bg=self.background_color)
        self.check_setup_complete()
    
    def select_color(self):
        """Open color selection dialog"""
        color = colorchooser.askcolor(title="Select Background Color")
        if color[1]:  # If a color was selected
            self.color_display.configure(bg=color[1])
            self.check_setup_complete()
    
    def check_setup_complete(self):
        """Check if all setup steps are complete and enable Next button"""
        name_ready = bool(self.name_var.get().strip())
        font_ready = bool(self.font_var.get().strip())
        color_ready = True  # Color always has a default

        if name_ready and font_ready and color_ready:
            self.next_btn.configure(state=tk.NORMAL)
        else:
            self.next_btn.configure(state=tk.DISABLED)

    def show_caption_window(self):
        """Show the caption creation window"""
        # Update settings from UI
        self.name = self.name_var.get()
        self.font_family = self.font_var.get()
        self.background_color = self.color_display.cget('bg')
        self.save_settings()

        # Clear the current window
        for widget in self.root.winfo_children():
            widget.destroy()

        self.root.title("Caption Creator - Define the caption")
        self.root.geometry("800x600")

        # Main frame
        main_frame = tk.Frame(self.root, bg=self.bg_color)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Image display area (70% height, upper right)
        image_frame = tk.Frame(main_frame, bg=self.bg_color)
        image_frame.pack(side=tk.TOP, fill=tk.BOTH, expand=True)

        self.image_label = tk.Label(image_frame, text="No photo selected", bg=self.text_bg_color,
                                   relief=tk.SUNKEN, width=50, height=20)
        self.image_label.pack(side=tk.RIGHT, padx=10, pady=10)

        # Control panel (left side and bottom)
        control_frame = tk.Frame(main_frame, bg=self.bg_color)
        control_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=10)

        # Buttons row 1
        button_frame1 = tk.Frame(control_frame, bg=self.bg_color)
        button_frame1.pack(fill=tk.X, pady=5)

        select_photo_btn = tk.Button(button_frame1, text="Select a photo", command=self.select_photo,
                                    font=("Arial", 11))
        select_photo_btn.pack(side=tk.LEFT, padx=5)

        start_over_btn = tk.Button(button_frame1, text="Start over again", command=self.start_over,
                                  font=("Arial", 11))
        start_over_btn.pack(side=tk.LEFT, padx=5)

        back_btn = tk.Button(button_frame1, text="Back to Setup", command=self.show_setup_window,
                            font=("Arial", 11))
        back_btn.pack(side=tk.RIGHT, padx=5)

        # Caption input
        caption_frame = tk.Frame(control_frame, bg=self.bg_color)
        caption_frame.pack(fill=tk.X, pady=10)

        tk.Label(caption_frame, text="Caption:", bg=self.bg_color, font=("Arial", 12)).pack(anchor=tk.W)

        self.caption_var = tk.StringVar()
        self.caption_var.trace('w', self.update_caption_preview)

        self.caption_entry = tk.Entry(caption_frame, textvariable=self.caption_var,
                                     bg=self.text_bg_color, font=("Arial", 12))
        self.caption_entry.pack(fill=tk.X, pady=5)

        # Finalize button
        self.finalize_btn = tk.Button(control_frame, text="Finalize the caption",
                                     command=self.finalize_caption, state=tk.DISABLED,
                                     font=("Arial", 12, "bold"))
        self.finalize_btn.pack(pady=10)

    def select_photo(self):
        """Select and load a photo"""
        file_path = filedialog.askopenfilename(
            title="Select a photo",
            filetypes=[
                ("Image files", "*.jpg *.jpeg *.png *.bmp *.gif *.tiff"),
                ("JPEG files", "*.jpg *.jpeg"),
                ("PNG files", "*.png"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            try:
                self.photo_path = file_path
                self.original_photo = Image.open(file_path)
                self.create_caption_strip()
                self.display_photo()
                self.finalize_btn.configure(state=tk.NORMAL)

            except Exception as e:
                messagebox.showerror("Error", f"Could not load image: {e}")

    def create_caption_strip(self):
        """Create a caption strip and add it to the photo"""
        if not self.original_photo:
            return

        # Get photo dimensions
        photo_width, photo_height = self.original_photo.size

        # Create caption strip (5% of photo height)
        strip_height = max(int(photo_height * 0.05), 20)  # Minimum 20 pixels

        # Create new image with caption strip
        new_height = photo_height + strip_height
        self.current_photo = Image.new('RGB', (photo_width, new_height), self.background_color)

        # Paste original photo on top
        self.current_photo.paste(self.original_photo, (0, 0))

        # Update caption preview
        self.update_caption_preview()

    def update_caption_preview(self, *args):
        """Update the caption text on the photo in real-time"""
        if not self.current_photo:
            return

        # Create a copy of the photo with caption strip
        photo_width, photo_height = self.original_photo.size
        strip_height = max(int(photo_height * 0.05), 20)

        # Recreate the image with caption strip
        preview_photo = Image.new('RGB', (photo_width, photo_height + strip_height), self.background_color)
        preview_photo.paste(self.original_photo, (0, 0))

        # Draw caption text
        draw = ImageDraw.Draw(preview_photo)

        # Calculate font size to fit strip height
        font_size = max(int(strip_height * 0.7), 12)

        try:
            # Try to use the selected font
            font = ImageFont.truetype(f"{self.font_family}.ttf", font_size)
        except:
            try:
                # Fallback to system font
                font = ImageFont.truetype("arial.ttf", font_size)
            except:
                # Final fallback to default font
                font = ImageFont.load_default()

        # Get caption text
        caption_text = self.caption_var.get()

        # Calculate positions
        strip_y = photo_height

        # Draw caption text (starting at 3rd character position - approximately 2 character widths)
        if caption_text:
            char_width = font.getbbox("M")[2]  # Width of 'M' character as reference
            caption_x = char_width * 2  # 2 character widths from left
            caption_y = strip_y + (strip_height - font_size) // 2

            draw.text((caption_x, caption_y), caption_text, fill="black", font=font)

        # Draw name at right end (2 character widths from right)
        if self.name:
            name_bbox = draw.textbbox((0, 0), self.name, font=font)
            name_width = name_bbox[2] - name_bbox[0]
            char_width = font.getbbox("M")[2]
            name_x = photo_width - name_width - (char_width * 2)  # 2 character widths from right
            name_y = strip_y + (strip_height - font_size) // 2

            draw.text((name_x, name_y), self.name, fill="black", font=font)

        self.current_photo = preview_photo
        self.display_photo()

    def display_photo(self):
        """Display the current photo in the UI"""
        if not self.current_photo:
            return

        # Resize for display (maintain aspect ratio)
        display_photo = self.current_photo.copy()
        display_photo.thumbnail((400, 300), Image.Resampling.LANCZOS)

        # Convert to PhotoImage for tkinter
        self.photo_image = ImageTk.PhotoImage(display_photo)
        self.image_label.configure(image=self.photo_image, text="")

    def finalize_caption(self):
        """Save the captioned photo back to original location"""
        if not self.current_photo or not self.photo_path:
            return

        try:
            # Save the captioned photo back to original location
            self.current_photo.save(self.photo_path)
            messagebox.showinfo("Success", f"Caption added and saved to {os.path.basename(self.photo_path)}")

            # Reset for next photo
            self.start_over()

        except Exception as e:
            messagebox.showerror("Error", f"Could not save image: {e}")

    def start_over(self):
        """Reset the current photo and caption"""
        self.photo_path = None
        self.original_photo = None
        self.current_photo = None
        self.caption_var.set("")
        self.image_label.configure(image="", text="No photo selected")
        self.finalize_btn.configure(state=tk.DISABLED)

def main():
    app = CaptionCreator()
    app.show_starter_window()

if __name__ == "__main__":
    main()
