# PoLAN: Command between Host and Client

- Host: Agent App
- Client: Display Audio (DA/Hub), Speaker, Camera, etc.

### UDP
- <PERSON><PERSON><PERSON> (Host) broadcasts / unicasts on the port 16140 via UDP  
- <PERSON><PERSON><PERSON> (Host) listens on the port 16130 via UDP  
- Client broadcasts / unicasts on the port 16130 via UDP  
- Client listens on the port 16140 via UDP  

### TCP

Host assigns a port to each Client.  
The ranges of the assigning port are shown below in the table.

| Type | Port |
| --- | --- |
| Command | 16201 - 16299 |
| Audio | 16301 - 16399 |
| Images | 16401 - 16499 |

## 1. Message Format

The message format is shown in the table.  
1. The first 5 bytes are preset representing "POLAN"
2. The next one byte is the TransactionID that starts off of 0x00 and increments up until 0xFF. Messages keep the same TransactionID as far as the messages are in the request and the reply in the same command.
3. The next 7th through 22nd bytes are all zeroes
4. The next 23rd byte represents CommandType, which is detailed in the next section.
5. The 24th byte is ACK flags. Sender sets 0x00. Receiver sends back with 0x01 in case of no error, or 0x02 otherwise.
6. The next 4 bytes from 25th throuth 28th shows the parameter size. eg. 30byte --> [24]=0x00 [25]=0x00 [26]=0x00 [27]=0x1E
7. The next 29th byte and later is the set of bytes of the parameter detailed in the later sections.

| Type |  |
| -- | -- |
| "POLAN" | [0]=0x50, [1]=0x4F, [2]=0x50, [3]=0x4F, [4]=0x4E |
| TransactionID | [5]=0x00 - 0xFF (increments and back to 00 on reaching FF) |
| TimeStamp<br>AccessToken | [6] through [21] = 0x00 |
| CommandType | [22]=CommandType ([see below](#2-commandtype)) |
| ACK Flag | [23]=ACK Flag ([see below](#3-ack-flag)) |
| Parameter Size | [24] through [27]: Parameter Size (32bit)<br>eg. 30byte -> [24]=0x00, [25]=0x00, [26]=0x00, [27]=0x1E |
| Parameter | [28] and later: Parameter |

Max message length is 1200  

## 2. CommandType

### 2.1 Host to Client

1. Host broadcasts the command `0x00` to search client via **UDP**. Parameter contains "Protocol Version" and "IP Address" of the host. The first two bytes represents major and minor version number. The next byte is `0x04` representing IPv4, and the last 4 bytes represents the IP address. eg. If the version is v1.2, and IP Address is ************ under IPv4, the bytes are `01 02 04 C0 A8 00 1B`  
2. Host sends the command `0x03` to request a client to connect via **UDP**. Parameter contains the assigning port and the IP address of the host. eg. TCP port 16201 and Host's IP address *************, the bytes are `00 3F 49 04 C0 A8 C1 02`  
3. Host sends the command `0x08` to reply to a client a result of the authentication. <font color="red">**Parameter is `0x00` for success and `0x01` for fail.**</font>  
4. Host sends the command `0x0F` to notify the procedure of the connection to the client is completed. Parameter is `0x00` for success and `0x01` for fail.
5. CommandType `0x10`, `0x12` and `0x13` are not implemented this time yet.
6. Host sends the command `0x50` to send a request to the client to display an image. The parameter is described in the section 2.3.
7. Host sends the command `0x52` to notify the client the status of transferring the image. `0x00` Default, `0x01` Initiated and `0x02` Completed.
8. Host sends the command `0x53` to send image data to the client. Parameter is detailed in the section 2.4.
9. Host sends the command `0x56` to request a client to transfer an image to another client. Parameter is the client's port and IP. eg. port 16201 and IP ************* --> `0x 3F 49 04 C0 A8 C1 02`  
10. Host sends the command `0x61` to notify a client of the status of listening mode. Parameter is `0x00` for Default, `0x01` for Started, `0x02` for Recording, and `0x03` for Stopped.  
11. Host sends the command `0x62` to request a client for setting the listening mode status. Parameter is `0x00` for Default, `0x01` for Started, `0x02` for Stopped.  
12. Host sends the command `0x70` to notify the client of the host's audio transferring status. `0x00` Default, `0x01` Initiated and `0x02` Completed.
13. Host sends the command `0x71` to send audio data to the client. Parameter is detailed in the section 2.4.

|  CommandType  |  Type  |  Parameter  | sample |
| ----| ---- | ----| ---- |
| `0x00` | Client Search (**UDP**) | ProtocolVersion<br>IPAddress | v1.2<br>IPv4 ************<br>`01 02 04 C0 A8 00 1B` |
| `0x03` | Connection Request (**UDP**) | TCP port<br>IPAddress | 16201<br>IPv4 *************<br>`00 3F 49 04 C0 A8 C1 02` |
| `0x08` | Auth Reply | Authentication Result Reply from Host to Client | - |
| `0x0F` | Link Finish | Linkage Result | `0x00` Success<br>`0x01` Fail |
| `0x10` | Get DevInfo | Request Device Info (no parameter) |  |
| `0x12` | Get SettingList | Get Device Settings |  |
| `0x13` | Set Setting | Set Device Settings |  |
| `0x50` | YMR Display Request | Send Image Display Request | [see below](#23-ymr-display-information) |
| `0x52` | Notify Transfer Status | Notify Image Transfer Status | `0x00` Default<br>`0x01` Initiated<br>`0x02` Completed |
| `0x53` | Screen Transfer | Screen Image Data Transfer | [see below](#24-ymr-screen-transfer) |
| `0x56` | Display Transfer Request (C2C) | TCP port<br>IPAddress | 16201<br>IPv4 *************<br>`00 3F 49 04 C0 A8 C1 02` |
| `0x61` | Notify VR Status | Notify Listening Mode Status | `0x00` Default<br>`0x01` Started<br>`0x02` Recording<br>`0x03` Stopped |
| `0x62` | Request MICdata | Set Listening Mode Request | `0x00` Default<br>`0x01` Started<br>`0x02` Stopped |
| `0x70` | AudioData Transfer Status | Audio Data Transfer Status | `0x00` Default<br>`0x01` Initiated<br>`0x02` Completed |
| `0x71` | AudioData Transfer | Audio Data | [see below](#24-ymr-screen-transfer) |


### 2.2 Client to Host

1. Client sends the command `0x01` via **UDP** to search host. Parameter is the client's IP address. eg. if the client's IP address is IPv4 ************, `04 C0 A8 00 1B`  
2. Client sends the command `0x02` via **UDP** to host as the response for its search. Parameter contains the Protocol Version and IP address of the client. The first two bytes represents major and minor version number. The next byte is `0x04` representing IPv4, and the last 4 bytes represents the IP address. eg. If the version is v1.2, and IP Address is ************ under IPv4, the bytes are `01 02 04 C0 A8 00 1B`  
3. Client sends the command `0x04` to reply to the host a result of the connection. Parameter is `0x00` for success and `0x01` for fail.
4. Client sends the command `0x07` to host to request authentication. There is no parameter.
5. CommandType `0x11` and `0x14` are not implemented this time yet.
6. Client sends `0x51` to host to reply if screen transfer is required. `0x00` for required, and `0x01` for not required.
7. Client sends `0x54` to host to notify the displaying image. Parameter is detailed in the section 2.3.
8. Cient sends `0x55` to host to notify the position that the user touched on the screen. Parameter is detailed in the section 2.4.
9. Client sends `0x60` to host to notify that the Client started listening. There is no parameter.
10. Client sends `0x63` to host to send PCM data. Parameter contains the PCM data.
11. Client sends `0xFF` to host to notify that the client is being active.

|  CommandType  |  Type  |  Parameter  | sample |
| ----| ---- | ----| ---- |
| `0x01` | Host Search (**UDP**) | IPAddress | IPv4 ************<br>`04 C0 A8 00 1B` |
| `0x02` | Client Ready (**UDP**) | ProtocolVersion<br>IPAddress | v1.2<br>IPv4 ************<br>`01 02 04 C0 A8 00 1B` |
| `0x04` | Connection Reply |  | `0x00` Success<br>`0x01` Fail |
| `0x07` | Auth Request | Authentication Request from Client to Host | - |
| `0x11` | Notify DevInfo | Send Device Information |  |
| `0x14` | Notify Update Setting | Respond with Updated Device Settings |  |
| `0x51` | YMR Display Reply | Reply If Display is Needed | `0x00` Required<br>`0x01` No Need |
| `0x54` | YMR Display Notify | Notify Display Data | [see below](#23-ymr-display-information) |
| `0x55` | Notify Touch Position | Notify Touched Position | [see below](#25-notify-touch-position) |
| `0x60` | Notify StartVR | Notify to Start Recording | - |
| `0x63` | MICdata Transfer | Transfer PCM data | `<PCM Data>` |
| `0xFF` | KeepAlive | KeepAlive | - |

### 2.3 YMR Display Information

1. The first byte is Display Type. There are three types. `0x01` App, `0x02` Device, and `0x03` for App+Device
2. The second and the third bytes are the screen ratio, width and height. eg. if the screen ration is 8:3, the second and the third bytes are `0x08` and `0x03`. If the Display Type is Device, both bytes are `0x00`
3. The next 4 bytes are the screen resolution. The fourth and fifth bytes are the width, and the sixth and the seventh are the height. eg. if the screen resolution is 1280 width and 480 height, the bytes are `05 00 01 E0`. If the Display Type is Device, all bytes are `0x00`
4. The next byte is the Device Screen Type, which is defined in devinfo. `0x00` for Default and `0x01` for Camera. `0x00` if the Display Type is App.
5. The next byte is the Screen ID `0x01` through `0xFF`. `0x00` if the Display Type is App.
6. The tenth byte is `0x04`
7. The next four bytes are for the sender's IP address. eg. if the IP Address is ************, the bytes are `C0 A8 00 1B`  
8. The 14th byte is `0x04`
9. The next four bytes are for the receiver's IP address. eg. if the IP Address is ************, the bytes are `C0 A8 00 1B`  
10. The tenth through 19th bytes are used to transfer data between clients.

| Type | Code | Notes |
| -- | -- | -- |
| Display Type | `0x01`: App<br>`0x02`: Device<br>`0x03`: App+Device |  |
| Screen Ratio for App Display | [1]=`0x08`, [2]=`0x03` | width:height= 3:3\|8:3\|3:9 etc.<br>Applicable for DisplayType=`0x01` or `0x03` |
| Screen Resolution for App Display | [3]=`0x05`, [4]=`0x00`, [5]=`0x01`, [6]=`0xE0` | in case 1280x480<br>Applicable for DisplayType=`0x01` or `0x03` |
| Device Screen Type | `0x00`: Default<br>`0x01`: Camera | the type defined in devInfo<br>Applicable for DisplayType=`0x02` or `0x03` |
| Screen ID | `0x01` ~ `0xFF` | `0x00`: Default(inapplicable)<br>Applicable for DisplayType=`0x01` or `0x03` |
| IP Version | `0x04`: IPv4 |  |
| IP Address | IP Address of the Sender |
| IP Version | `0x04`: IPv4 |  |
| IP Address | IP Address of the Receiver |

[9] - [18] is used to transfer data between clients.

### 2.4 YMR Screen Transfer

1. The first four bytes are for the Total Data Size.
2. The next four bytes are for the data size sending in this message. If the transfer is done in a single message, this four bytes are all `0x00`
3. The data comes to the next byte and later.

| Type | Code | Notes |
| -- | -- | -- |
| Total Data Size | `0x01`~`0xFFFFFFFF` |  |
| Transfer Data Size | `0x01`~`0xFFFFFFFF` | in case of a single transfer: `0x00`Default |
| Transfer Data | Data |  |

### 2.5 Notify Touch Position

1. The coordinates of the position that the user touched is notified as (X, Y). eg. if the touched position is (X, Y)=(600, 100), it will be `02 58 11 64`

| Type | Code | Notes |
| -- | -- | -- |
| X | 600 | `02 58` |
| Y | 100 | `00 64` |

### 3. ACK Flag

1. ACK Flag is `0x00` in the sending message. `0x01` in the successfully responding message. Otherwise, `0xFF`

| Code | Status |
| --- | --- |
| `0x00` | sending... |
| `0x01` | ACK |
| `0xFF` | NACK |

- on TCP, responds with ACK or NACK
  - the same TransactionID and CommandType
  - no Parameter

- How to handle NACK: **TBD**

#### Communication Policy over TCP

Message between Host and Client over TCP
- When Host (or Client) sends a message to Client (or Host) over TCP, ACK flag is set `0x00` with Parameter if necessary.
- When Client (or Host) responds to the message, ACK flag is `0x01` for ACK or `0xFF` otherwise without Parameter.

