# PoLAN Client Server

Please create a server.
- name: <PERSON><PERSON>an <PERSON> Server
- Act as a Client, and communicate with the Host.
- Use Commands.md as a reference to the communication and message formats.
- Since the server is designed for testing purposes, some messages may be with dummy data. I will explain those.

- Suppose, the Protocol version is `1.2`
- 

## Establish a connection between host and client (Part of UC04)
There are two ways to establish the connection between host and client.
### scenario 1
1. Client receives a **broadcasting** "Client Search (`0x00`)" message via U<PERSON> from a host on the port 16140.
2. Client retrieves the host's IP address from the message and sends "Client Ready (`0x02`)" message via UDP with the client's IP address to the host.  
3. <PERSON><PERSON> receives "Connection Request (`0x03`)" message via UDP with the assigned TCP port and the host's IP address.
4. Client sends "Connection Reply (`0x04`)" message via TCP on the assigned port to the host with the parameter `0x00` as succeeded.

### scenario 2
1. <PERSON>lient (the Server) broadcasts "Host Search (`0x01`)" message via UDP on the port 16130 with the client's IP address.
2. <PERSON><PERSON> receives a **unicasting** "Client Search (`0x00`)" message via <PERSON><PERSON> from a host on the port 16140.
3. <PERSON><PERSON> retrieves the host's IP address from the message and sends "Client Ready (`0x02`)" message via UDP with the client's IP address to the host.  
4. Client receives "Connection Request (`0x03`)" message via UDP with the assigned TCP port and the host's IP address.
5. Client sends "Connection Reply (`0x04`)" message via TCP on the assigned port to the host with the parameter `0x00` as succeeded.


## UC01 YMR
### Draw Screen Image
Once the connection is established, Host sends screen images to Client to display  
1. Client receives "YMR Display Request (`0x50`)" message from the host.
2. Client checks the Screen ID in the parameter. If the Screen ID is in the cache, Client responds to the host with "YMR Display Reply (`0x51`)" message with `0x01` as "no need", otherwise `0x00`
3. If Client needs to receive the screen data, proceed to next. Otherwise, the process is completed here.
4. Client receives "Notify Transfer Status (`0x52`)" message with `0x01` to initiate to receive screen data.
5. Client receives "Screen Transfer (`0x53`)" message with the screen data and append the data as it receives until it receives "Notify Transfer Status (`0x52`)" message with `0x02`
6. Save the image locally as a jpeg file with the filename starting "screenimage" following the timestamp formatting "yyyymmddhhmmss" and ending with an extension ".jpg", and Client sends "YMR Display Notify (`0x54`)" message with the same parameter received in #1.

### Touch Screen
When a user touches the screen, the touched position (coordinates) will be sent to host. Since this server is a test server, th touched position will be randomly chosen but not too close to the edge of the screen image.
1. Client sends "Notify Touch Position (`0x55`)" message to notify the position the user touched to the host. As described above, the position is randomly chosen inside the screen image. make sure it's  not too close to the edge.

## UC02 Audio
Once the connection is establieshed, Client can start sending audio data to the host.
### Client sends audio data to Host
1. Client sends "Notify StartVR (`0x60`)" message to host without a parameter.
2. Client receives "Notify VR Status (`0x61`)" message with parameter `0x01`
3. Client receives "Request MicData (`0x62`)" message with parameter `0x01` from the host that asks Client to record and send PCM data to host.
4. Client sends "MicData Transfer (`0x63`)" message with PCM data.
5. Client receives "Request MicData (`0x62`)" message with parameter `0x02` from the host, and Client stops recording.

### Client receives
1. Client receives "Notify VR Status (`0x61`)" message with parameter `0x02`
2. Client receives "AudioData Transfer Status (`0x70`)" message with parameter `0x01`
3. Client receives "AudioData Transfer (`0x71`)" message with audio data. Client keeps receiving and append the data until it receives "AudioData Transfer Status (`0x70`)" message with parameter `0x02`
4. Save the audio data as a wave file. The file name starts with "audio" following the timestamp formatting "yyyymmddhhmmss" and ending with an extension ".wav"
